/**
 * Hook để xử lý xóa thread đơn giản (không active)
 * Chỉ gọi API xóa và update UI, không cần integration
 */

import { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ThreadsService } from '../services/threads.service';
import { THREADS_QUERY_KEYS } from '../constants';
import { markThreadAsDeleted } from '../services/deleted-thread-tracker.service';

interface UseSimpleThreadDeleteProps {
  /**
   * Callback khi xóa thành công
   */
  onThreadDeleted?: (threadId: string) => void;
}

export const useSimpleThreadDelete = ({ onThreadDeleted }: UseSimpleThreadDeleteProps = {}) => {
  const queryClient = useQueryClient();
  const [deletingThreadId, setDeletingThreadId] = useState<string | null>(null);

  // Mutation để delete thread đơn giản
  const deleteThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setDeletingThreadId(threadId);
      
      console.log('[useSimpleThreadDelete] Deleting non-active thread:', threadId);
      
      // Chỉ gọi API xóa
      await ThreadsService.deleteThread(threadId);
      
      return { deletedThreadId: threadId };
    },
    onSuccess: ({ deletedThreadId }) => {
      console.log('[useSimpleThreadDelete] Thread deleted successfully:', deletedThreadId);

      // Mark thread as deleted để prevent future API calls
      markThreadAsDeleted(deletedThreadId);

      // Remove thread detail từ cache
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });

      // Optimistic update: Remove thread từ tất cả list queries ngay lập tức
      queryClient.setQueriesData(
        {
          queryKey: THREADS_QUERY_KEYS.ALL,
          predicate: (query) => query.queryKey.includes('list') || query.queryKey.includes('paginated')
        },
        (oldData: any) => {
          if (!oldData) return oldData;

          // Handle infinite query data structure
          if (oldData.pages) {
            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                data: page.data ? page.data.filter((thread: any) => thread.threadId !== deletedThreadId) : []
              }))
            };
          }

          // Handle regular query data structure
          if (Array.isArray(oldData)) {
            return oldData.filter((thread: any) => thread.threadId !== deletedThreadId);
          }

          return oldData;
        }
      );

      // Invalidate queries để refetch fresh data (exclude detail queries để tránh 404)
      queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.ALL,
        predicate: (query) => {
          // Chỉ invalidate list/paginated queries, KHÔNG invalidate detail queries
          const isListQuery = query.queryKey.includes('list') || query.queryKey.includes('paginated');
          const isDetailQuery = query.queryKey.includes('detail');
          return isListQuery && !isDetailQuery;
        }
      });

      // Clear loading state
      setDeletingThreadId(null);

      // Trigger callback
      onThreadDeleted?.(deletedThreadId);
    },
    onError: (error) => {
      console.error('[useSimpleThreadDelete] Failed to delete thread:', error);
      setDeletingThreadId(null);
    },
  });

  // Function để delete thread
  const deleteThread = useCallback((threadId: string) => {
    deleteThreadMutation.mutate(threadId);
  }, [deleteThreadMutation]);

  // Function để check xem thread có đang được xóa không
  const isThreadDeleting = useCallback((threadId: string) => {
    return deletingThreadId === threadId;
  }, [deletingThreadId]);

  return {
    // Actions
    deleteThread,
    
    // State
    isThreadDeleting,
    isDeleting: deleteThreadMutation.isPending,
    error: deleteThreadMutation.error,
  };
};
